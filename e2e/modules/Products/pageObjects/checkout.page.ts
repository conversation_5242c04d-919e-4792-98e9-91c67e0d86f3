import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../../../utils/basePage';

/**
 * Checkout page object representing the checkout page
 */
export class CheckoutPage extends BasePage {
  readonly orderSummaryItems: Locator;
  readonly orderSummaryTotal: Locator;
  readonly orderSummarySubtotal: Locator;
  readonly orderSummaryTax: Locator;
  readonly orderSummaryShipping: Locator;
  readonly billingNameInput: Locator;
  readonly billingEmailInput: Locator;
  readonly billingAddressInput: Locator;
  readonly billingCityInput: Locator;
  readonly billingStateInput: Locator;
  readonly billingZipInput: Locator;
  readonly billingCountryInput: Locator;
  readonly billingPhoneInput: Locator;
  readonly paymentMethodRadios: Locator;
  readonly creditCardNumberInput: Locator;
  readonly creditCardExpiryInput: Locator;
  readonly creditCardCvvInput: Locator;
  readonly creditCardNameInput: Locator;
  readonly placeOrderButton: Locator;
  readonly backToCartButton: Locator;
  readonly orderConfirmationMessage: Locator;
  readonly orderNumber: Locator;

  /**
   * Initialize the Checkout page object
   * @param page Playwright page
   */
  constructor(page: Page) {
    super(page);
    this.orderSummaryItems = page.locator('[data-testid="order-summary-item"]');
    this.orderSummaryTotal = page.locator('[data-testid="order-summary-total"]');
    this.orderSummarySubtotal = page.locator('[data-testid="order-summary-subtotal"]');
    this.orderSummaryTax = page.locator('[data-testid="order-summary-tax"]');
    this.orderSummaryShipping = page.locator('[data-testid="order-summary-shipping"]');
    this.billingNameInput = page.locator('[data-testid="billing-name-input"]');
    this.billingEmailInput = page.locator('[data-testid="billing-email-input"]');
    this.billingAddressInput = page.locator('[data-testid="billing-address-input"]');
    this.billingCityInput = page.locator('[data-testid="billing-city-input"]');
    this.billingStateInput = page.locator('[data-testid="billing-state-input"]');
    this.billingZipInput = page.locator('[data-testid="billing-zip-input"]');
    this.billingCountryInput = page.locator('[data-testid="billing-country-input"]');
    this.billingPhoneInput = page.locator('[data-testid="billing-phone-input"]');
    this.paymentMethodRadios = page.locator('[data-testid="payment-method-radio"]');
    this.creditCardNumberInput = page.locator('[data-testid="credit-card-number-input"]');
    this.creditCardExpiryInput = page.locator('[data-testid="credit-card-expiry-input"]');
    this.creditCardCvvInput = page.locator('[data-testid="credit-card-cvv-input"]');
    this.creditCardNameInput = page.locator('[data-testid="credit-card-name-input"]');
    this.placeOrderButton = page.locator('[data-testid="place-order-button"]');
    this.backToCartButton = page.locator('[data-testid="back-to-cart-button"]');
    this.orderConfirmationMessage = page.locator('[data-testid="order-confirmation-message"]');
    this.orderNumber = page.locator('[data-testid="order-number"]');
  }

  /**
   * Navigate to the checkout page
   */
  async goto() {
    await this.page.goto('/trq/purchases/checkout');
    await this.waitForPageLoad();
  }

  /**
   * Wait for the checkout page to load
   */
  async waitForPageLoad() {
    await this.page.waitForSelector('[data-testid="order-summary-total"]', { state: 'visible' });
  }

  /**
   * Fill in the billing information
   * @param billingInfo The billing information
   */
  async fillBillingInfo(billingInfo: {
    name: string;
    email: string;
    address: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    phone: string;
  }) {
    await this.billingNameInput.fill(billingInfo.name);
    await this.billingEmailInput.fill(billingInfo.email);
    await this.billingAddressInput.fill(billingInfo.address);
    await this.billingCityInput.fill(billingInfo.city);
    await this.billingStateInput.fill(billingInfo.state);
    await this.billingZipInput.fill(billingInfo.zip);
    await this.billingCountryInput.selectOption(billingInfo.country);
    await this.billingPhoneInput.fill(billingInfo.phone);
  }

  /**
   * Select a payment method
   * @param method The payment method (e.g., 'credit-card', 'paypal')
   */
  async selectPaymentMethod(method: string) {
    await this.page.locator(`[data-testid="payment-method-radio"][value="${method}"]`).click();
  }

  /**
   * Fill in the credit card information
   * @param cardInfo The credit card information
   */
  async fillCreditCardInfo(cardInfo: { number: string; expiry: string; cvv: string; name: string }) {
    await this.creditCardNumberInput.fill(cardInfo.number);
    await this.creditCardExpiryInput.fill(cardInfo.expiry);
    await this.creditCardCvvInput.fill(cardInfo.cvv);
    await this.creditCardNameInput.fill(cardInfo.name);
  }

  /**
   * Place the order
   */
  async placeOrder() {
    await this.placeOrderButton.click();
    // Wait for order confirmation
    await this.page.waitForSelector('[data-testid="order-confirmation-message"]', { state: 'visible' });
  }

  /**
   * Go back to the cart
   */
  async backToCart() {
    await this.backToCartButton.click();
    // Wait for navigation to cart page
    await this.page.waitForURL('**/purchases/cart');
  }

  /**
   * Get the order number after placing an order
   * @returns The order number
   */
  async getOrderNumber(): Promise<string> {
    return (await this.orderNumber.textContent()) || '';
  }

  /**
   * Get the order total
   * @returns The order total
   */
  async getOrderTotal(): Promise<string> {
    return (await this.orderSummaryTotal.textContent()) || '';
  }

  /**
   * Verify that the order was placed successfully
   * @returns True if the order was placed successfully, false otherwise
   */
  async verifyOrderPlaced(): Promise<boolean> {
    return await this.orderConfirmationMessage.isVisible();
  }

  /**
   * Complete the checkout process with default test data
   */
  async completeCheckout() {
    // Fill in billing information with test data
    await this.fillBillingInfo({
      name: 'Test User',
      email: '<EMAIL>',
      address: '123 Test St',
      city: 'Test City',
      state: 'CA',
      zip: '12345',
      country: 'United States',
      phone: '************'
    });

    // Select credit card payment method
    await this.selectPaymentMethod('credit-card');

    // Fill in credit card information with test data
    await this.fillCreditCardInfo({
      number: '****************',
      expiry: '12/25',
      cvv: '123',
      name: 'Test User'
    });

    // Place the order
    await this.placeOrder();

    // Verify order was placed successfully
    return await this.verifyOrderPlaced();
  }
}
