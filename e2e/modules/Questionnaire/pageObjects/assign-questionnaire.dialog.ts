import { Page, Locator, expect } from '@playwright/test';

export class AssignQuestionnaireDialog {
  readonly page: Page; // Technically the dialog exists within a page
  readonly dialog: Locator;
  readonly searchInput: Locator;
  readonly submitButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.dialog = page.locator('[data-testid="assign-questionnaire-dialog"]');
    this.searchInput = this.dialog.locator('[data-testid="patient-search-input"]');
    this.submitButton = this.dialog.locator('[data-testid="dialog-submit-button"]');
  }

  async waitForDialog() {
    await expect(this.dialog).toBeVisible();
  }

  getPatientLabel(patientId: string): Locator {
    // Ideal selector using dynamic test ID
    return this.dialog.locator(`[data-testid="patient-select-label-${patientId}"]`);
    // Fallback:
    // return this.dialog.locator(`label:has-text("${patientName}")`);
  }

  async selectPatient(patientId: string, patientName?: string) {
    // TODO: Replace placeholder ID logic if possible
    const patientLabel = this.getPatientLabel(patientId || 'placeholder-id');
    // Fallback:
    // const patientLabel = this.dialog.locator(`label:has-text("${patientName}")`);
    await expect(patientLabel).toBeVisible();
    await patientLabel.click();
  }

  async submit() {
    await expect(this.submitButton).toBeEnabled();
    await this.submitButton.click();
    await expect(this.dialog).not.toBeVisible(); // Wait for dialog to close
  }
}
