import { Page, Locator, expect } from '@playwright/test';

export class AllQuestionnairesPage {
  readonly page: Page;
  readonly dataGrid: Locator;
  readonly createFromTemplateButton: Locator;
  readonly assignToolbarButton: Locator;
  readonly deleteToolbarButton: Locator; // Added
  readonly searchInput: Locator;
  readonly statusFilter: Locator;
  // Confirmation Dialog Locators (assuming standard MUI structure)
  readonly confirmationDialog: Locator;
  readonly confirmDeleteButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.dataGrid = page.locator('[data-testid="questionnaire-data-grid"]');
    this.createFromTemplateButton = page.locator('[data-testid="create-questionnaire-from-template-button"]');
    this.assignToolbarButton = page.locator('[data-testid="questionnaire-assign-button"]');
    // Toolbar delete button - assuming it appears when rows are selected
    this.deleteToolbarButton = page.locator('.MuiToolbar-root button:has-text("Delete")'); // Might need a data-testid
    this.searchInput = page.locator('[data-testid="questionnaire-search-input"]');
    this.statusFilter = page.locator('[data-testid="questionnaire-status-filter"]');
    // Confirmation Dialog - assuming it's a Dialog with specific title/text
    this.confirmationDialog = page.locator('div[role="dialog"]:has-text("Confirm Delete")');
    this.confirmDeleteButton = this.confirmationDialog.locator('button:has-text("Delete")');
  }

  async goto() {
    await this.page.goto('/trq/questionnaires');
    await expect(this.dataGrid).toBeVisible();
  }

  getQuestionnaireRow(title: string): Locator {
    // Still relies on text, as ID might not be known beforehand
    return this.dataGrid.locator(`.MuiDataGrid-row:has-text("${title}")`);
  }

  getRowCheckbox(rowLocator: Locator): Locator {
    // Selector for the checkbox within a specific row
    return rowLocator.locator('.MuiCheckbox-root input');
  }

  getAssignButtonForRow(rowLocator: Locator, questionnaireId: string): Locator {
    // Ideal selector using dynamic test ID
    return rowLocator.locator(`[data-testid="questionnaire-row-assign-button-${questionnaireId}"]`);
    // Fallback:
    // return rowLocator.locator('.MuiDataGrid-cell[data-field="patientId"] button:has-text("Assign")');
  }

  async clickCreateFromTemplate() {
    await this.createFromTemplateButton.click();
  }

  async clickAssignForRow(rowLocator: Locator, questionnaireId: string) {
    // TODO: Replace placeholder ID logic if possible
    const assignButton = this.getAssignButtonForRow(rowLocator, questionnaireId || 'placeholder-id');
    await assignButton.click();
  }

  async verifyQuestionnaireAssigned(rowLocator: Locator, patientName: string) {
    const assignedToCell = rowLocator.locator(`.MuiDataGrid-cell[data-field="patientId"]:has-text("${patientName}")`);
    await expect(assignedToCell).toBeVisible();
    const statusCell = rowLocator.locator('.MuiDataGrid-cell[data-field="status"] .MuiChip-label:has-text("Assigned")');
    await expect(statusCell).toBeVisible();
  }

  async verifyQuestionnaireCreated(rowLocator: Locator) {
    await expect(rowLocator).toBeVisible({ timeout: 10000 });
    const statusCell = rowLocator.locator('.MuiDataGrid-cell[data-field="status"] .MuiChip-label:has-text("Created")');
    await expect(statusCell).toBeVisible();
  }

  async selectRowByTitle(title: string) {
    const row = this.getQuestionnaireRow(title);
    await expect(row).toBeVisible();
    const checkbox = this.getRowCheckbox(row);
    await checkbox.check();
    // Add a small wait or assertion to ensure selection registers if needed
    await expect(this.deleteToolbarButton).toBeVisible(); // Delete button should appear
  }

  async clickToolbarDelete() {
    await expect(this.deleteToolbarButton).toBeEnabled();
    await this.deleteToolbarButton.click();
  }

  async confirmDeletion() {
    await expect(this.confirmationDialog).toBeVisible();
    await expect(this.confirmDeleteButton).toBeEnabled();
    await this.confirmDeleteButton.click();
    await expect(this.confirmationDialog).not.toBeVisible(); // Wait for dialog to close
  }

  async verifyQuestionnaireDeleted(title: string) {
    const row = this.getQuestionnaireRow(title);
    await expect(row).not.toBeVisible(); // Verify the row is gone
  }
}
