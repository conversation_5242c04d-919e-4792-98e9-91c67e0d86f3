import { test, expect } from '@playwright/test';
import { loginAs } from '../../utils/auth-utils';
import { Role } from '../../../src/[types]/Role'; // Correct import for Role enum
import { AllQuestionnairesPage } from './pageObjects/all-questionnaires.page';
import { CreateQuestionnairePage } from './pageObjects/create-questionnaire.page';
import { AssignQuestionnaireDialog } from './pageObjects/assign-questionnaire.dialog';
import { TemplateSelectionDialog } from './pageObjects/template-selection.dialog';

test.describe('Admin Questionnaire Management', () => {
  let allQuestionnairesPage: AllQuestionnairesPage;
  let createQuestionnairePage: CreateQuestionnairePage;
  let assignDialog: AssignQuestionnaireDialog;
  let templateDialog: TemplateSelectionDialog;

  // Add baseURL to the fixture destructuring
  test.beforeEach(async ({ page, baseURL }) => {
    // Instantiate POMs
    allQuestionnairesPage = new AllQuestionnairesPage(page);
    createQuestionnairePage = new CreateQuestionnairePage(page);
    assignDialog = new AssignQuestionnaireDialog(page);
    templateDialog = new TemplateSelectionDialog(page);

    // Log in as an admin before each test
    // Use Role.Admin and pass baseURL
    // Ensure baseURL is defined; add a fallback or check if needed
    if (!baseURL) {
      // No longer need baseURL check here
    }
    // Log in before each test, always pass baseURL from test context
    await loginAs(page, Role.Admin, baseURL);
    // TODO: Navigate to the initial page if necessary, e.g., dashboard
    // await page.goto('/trq/dashboard'); // Example: Navigate after login if needed
  });

  test('should allow an admin to create a new questionnaire', async () => {
    // 1. Navigate to questionnaire creation page
    await createQuestionnairePage.goto();

    // 2. Fill in questionnaire details
    const questionnaireName = `Test Questionnaire Name ${Date.now()}`;
    const questionnaireTitle = `Test Questionnaire Title ${Date.now()}`;
    const questionnaireDescription = 'This is a test description for the questionnaire.';
    await createQuestionnairePage.fillDetails(questionnaireName, questionnaireTitle, questionnaireDescription);

    // 3. Save the questionnaire
    await createQuestionnairePage.save();

    // 4. Assert that the questionnaire was created successfully
    await createQuestionnairePage.verifySuccess();

    // Optional: Verify the questionnaire appears in the list
    // await allQuestionnairesPage.goto(); // Need to navigate back if verifySuccess didn't land here
    // const newRow = allQuestionnairesPage.getQuestionnaireRow(questionnaireTitle);
    // await expect(newRow).toBeVisible();
  });

  test('should allow an admin to assign a questionnaire to a patient', async () => {
    // TODO: Reliably ensure an unassigned questionnaire and a target patient exist before this test runs.
    // This might involve API calls in a global setup or beforeAll/beforeEach.
    const questionnaireTitle = 'Test Questionnaire for Assignment'; // Placeholder: Use a known, unassigned questionnaire title
    const placeholderQuestionnaireId = 'test-assign-id'; // Placeholder: Replace with actual ID from setup
    const patientName = 'Test Patient Assignee'; // Placeholder: Use a known patient name/email
    const placeholderPatientId = 'test-patient-assignee-id'; // Placeholder: Replace with actual ID from setup

    // 1. Navigate to questionnaire list page
    await allQuestionnairesPage.goto();

    // 2. Find the questionnaire row
    const questionnaireRow = allQuestionnairesPage.getQuestionnaireRow(questionnaireTitle);
    await expect(questionnaireRow).toBeVisible();

    // 3. Click the assign button for the row to open the dialog
    await allQuestionnairesPage.clickAssignForRow(questionnaireRow, placeholderQuestionnaireId);

    // 4. Wait for the dialog and select the patient
    await assignDialog.waitForDialog();
    await assignDialog.selectPatient(placeholderPatientId, patientName); // Pass name as fallback if needed

    // 5. Submit the assignment
    await assignDialog.submit();

    // 6. Assert that the questionnaire is assigned in the grid
    // Re-locate the row to check updated state
    const updatedQuestionnaireRow = allQuestionnairesPage.getQuestionnaireRow(questionnaireTitle);
    await allQuestionnairesPage.verifyQuestionnaireAssigned(updatedQuestionnaireRow, patientName);
  });

  test('should allow an admin to create and then assign a questionnaire', async () => {
    // TODO: Reliably ensure the target patient exists before this test runs.
    const newQuestionnaireName = `E2E Test Name ${Date.now()}`;
    const newQuestionnaireTitle = `E2E Test Title ${Date.now()}`;
    const newQuestionnaireDescription = 'Created and assigned via E2E test.';
    const patientName = 'Test Patient Full Flow'; // Placeholder: Use a known patient name/email
    const placeholderPatientId = 'test-patient-full-flow-id'; // Placeholder: Replace with actual ID from setup

    // --- Create Questionnaire ---
    await createQuestionnairePage.goto();
    await createQuestionnairePage.fillDetails(newQuestionnaireName, newQuestionnaireTitle, newQuestionnaireDescription);
    await createQuestionnairePage.save();
    await createQuestionnairePage.verifySuccess(); // Lands on AllQuestionnairesPage

    // --- Assign Questionnaire ---
    // Page is already on AllQuestionnairesPage

    // Find the newly created questionnaire row
    // TODO: Need the actual ID of the newly created questionnaire to use the dynamic test ID.
    const placeholderNewQuestionnaireId = 'new-e2e-test-id'; // Replace with actual ID
    const questionnaireRow = allQuestionnairesPage.getQuestionnaireRow(newQuestionnaireTitle);
    await expect(questionnaireRow).toBeVisible();

    // Click assign button for the row
    await allQuestionnairesPage.clickAssignForRow(questionnaireRow, placeholderNewQuestionnaireId);

    // Wait for dialog, select patient
    await assignDialog.waitForDialog();
    await assignDialog.selectPatient(placeholderPatientId, patientName);

    // Confirm assignment in dialog
    await assignDialog.submit();

    // Assert grid updated
    const updatedQuestionnaireRow = allQuestionnairesPage.getQuestionnaireRow(newQuestionnaireTitle);
    await allQuestionnairesPage.verifyQuestionnaireAssigned(updatedQuestionnaireRow, patientName);
  });

  test('should allow an admin to create a questionnaire from a template', async () => {
    // 1. Navigate to questionnaire list page
    await allQuestionnairesPage.goto();

    // 2. Click the "Create from Template" button
    await allQuestionnairesPage.clickCreateFromTemplate();

    // 3. Wait for the template selection dialog
    await templateDialog.waitForDialog();

    // 4. Select a template (e.g., General Health Assessment)
    const templateId = 'general-health'; // ID of the template to select
    await templateDialog.selectTemplate(templateId);

    // 5. Click the "Create Questionnaire" button in the dialog
    await templateDialog.submit();

    // 6. Assert that a new questionnaire (created from the template) appears in the list
    // The title might be generic initially, or use the template title. Assuming template title for now.
    const templateTitle = 'General Health Assessment'; // Title of the selected template
    const newQuestionnaireRow = allQuestionnairesPage.getQuestionnaireRow(templateTitle);
    await allQuestionnairesPage.verifyQuestionnaireCreated(newQuestionnaireRow);
  });

  test('should allow an admin to create and delete a questionnaire', async () => {
    // --- Create Questionnaire ---
    await createQuestionnairePage.goto();
    const questionnaireName = `E2E Delete Test Name ${Date.now()}`;
    const questionnaireTitle = `E2E Delete Test Title ${Date.now()}`;
    const questionnaireDescription = 'This questionnaire will be deleted.';
    await createQuestionnairePage.fillDetails(questionnaireName, questionnaireTitle, questionnaireDescription);
    await createQuestionnairePage.save();
    await createQuestionnairePage.verifySuccess(); // Lands on AllQuestionnairesPage

    // --- Delete Questionnaire ---
    // Page should be on AllQuestionnairesPage

    // Select the row of the newly created questionnaire
    await allQuestionnairesPage.selectRowByTitle(questionnaireTitle);

    // Click the delete button in the toolbar
    await allQuestionnairesPage.clickToolbarDelete();

    // Confirm the deletion in the dialog
    await allQuestionnairesPage.confirmDeletion();

    // Verify the questionnaire is no longer in the list
    await allQuestionnairesPage.verifyQuestionnaireDeleted(questionnaireTitle);
  });
});
