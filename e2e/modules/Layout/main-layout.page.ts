import { Page, Locator, expect } from '@playwright/test';

export class MainLayoutPage {
  readonly page: Page;
  readonly sidebar: Locator;

  constructor(page: Page) {
    this.page = page;
    // Assuming the sidebar nav element exists and contains the nav items
    this.sidebar = page.locator('nav[aria-label="mailbox folders"]');
  }

  getNavItem(itemId: string): Locator {
    // Locator for the specific nav item using its data-testid
    return this.sidebar.locator(`[data-testid="nav-item-${itemId}"]`);
  }

  async clickNavItem(itemId: string) {
    const navItem = this.getNavItem(itemId);
    await expect(navItem).toBeVisible();

    // Check if the item is inside a collapsed section (needs specific selectors based on NavCollapse structure)
    // This might involve checking parent elements or aria-expanded attributes.
    // For simplicity now, assuming direct click works or parent is already expanded.
    // TODO: Add logic to expand parent NavCollapse if necessary.

    await navItem.click();
  }

  async isNavItemVisible(itemId: string): Promise<boolean> {
    return this.getNavItem(itemId).isVisible();
  }

  // Helper to verify navigation and potentially wait for a key element on the target page
  async verifyNavigation(expectedUrl: string | RegExp, pageContentSelector?: string) {
    // Only check the path, not the host
    const currentPath = new URL(this.page.url()).pathname;
    if (typeof expectedUrl === 'string') {
      expect(currentPath).toBe(expectedUrl);
    } else {
      expect(currentPath).toMatch(expectedUrl);
    }
    if (pageContentSelector) {
      await expect(this.page.locator(pageContentSelector)).toBeVisible();
    }
  }

  // Opens all menu groups and collapses in the sidebar so all nav items are visible
  async openAllMenuGroups() {
    // Open all nav groups
    const navGroups = this.sidebar.locator('[data-testid^="nav-group-btn-"]');
    const groupCount = await navGroups.count();
    for (let i = 0; i < groupCount; i++) {
      const groupBtn = navGroups.nth(i);
      // Only click if not expanded (e.g. aria-expanded or class check if available)
      if (await groupBtn.isVisible()) {
        // Optionally check for expanded state if implemented
        await groupBtn.click({ force: true });
      }
    }
    // Open all collapses
    const navCollapses = this.sidebar.locator('[data-testid^="nav-collapse-btn-"]');
    const collapseCount = await navCollapses.count();
    for (let i = 0; i < collapseCount; i++) {
      const collapseBtn = navCollapses.nth(i);
      if (await collapseBtn.isVisible()) {
        await collapseBtn.click({ force: true });
      }
    }
  }
}
