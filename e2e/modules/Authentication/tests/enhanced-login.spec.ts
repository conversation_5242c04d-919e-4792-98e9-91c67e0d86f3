import { test, expect } from '@playwright/test';
import { Role } from '../../../../src/[types]/Role';
import { LoginPage } from '../pageObjects/login.page';

/**
 * Enhanced E2E Tests for user login with all roles
 * This test suite includes better debugging and more reliable verification
 */
test.describe('Enhanced Role-based Login Tests', () => {
  // Define the base URL (can be overridden by environment variable)
  const baseURL = process.env.BASE_URL || 'http://localhost:3001';

  // Debug mode for detailed logging
  const DEBUG = process.env.DEBUG_TESTS === 'true' || process.env.DEBUG === 'true';

  // Array of ALL roles to test (including Admin)
  const allRoles = [Role.Admin, Role.ClinicAdmin, Role.Doctor, Role.Client, Role.Patient];

  // Setup to run before each test
  test.beforeEach(async ({ page }) => {
    // Log test environment info
    if (DEBUG) {
      console.log(`\nTest Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`Base URL: ${baseURL}`);
      console.log(`Viewport: ${page.viewportSize()?.width}x${page.viewportSize()?.height}`);

      // Add verbose page event logging for debugging
      page.on('console', msg => console.log(`[Browser Console] ${msg.type()}: ${msg.text()}`));
      page.on('pageerror', err => console.error(`[Browser Error] ${err}`));
      page.on('request', req => console.log(`[Request] ${req.method()} ${req.url()}`));
      page.on('response', res => console.log(`[Response] ${res.status()} ${res.url()}`));
    }
  });

  // Test each role with full verification
  for (const role of allRoles) {
    test(`Login test for ${role} role`, async ({ page }) => {
      const loginPage = new LoginPage(page, DEBUG);

      // Enable detailed logs specifically for this test
      if (DEBUG) {
        loginPage.enableDebug();
      }

      // Step 1: Navigate to login page
      await test.step(`Navigate to login page for ${role}`, async () => {
        test.info().annotations.push({
          type: 'Test Step',
          description: `Navigating to login page for ${role} role`
        });

        await loginPage.navigateToLoginPage(baseURL);

        // Verify we're on the login page
        const loginForm = page.getByTestId('login-email-input');
        await expect(loginForm, 'Login form should be visible').toBeVisible();

        // Take screenshot of login form
        await loginPage.takeScreenshot(`login-form-${role}`);
      });

      // Step 2: Fill login form with role-specific credentials
      await test.step(`Fill login form for ${role}`, async () => {
        // Get credentials for the current role
        const credentials = loginPage.getCredentialsForRole(role);

        test.info().annotations.push({
          type: 'Credentials',
          description: `Using email: ${credentials.email} for ${role} role`
        });

        // Fill the login form
        await loginPage.fillLoginForm(credentials.email, credentials.password);

        // Take screenshot of filled form
        await loginPage.takeScreenshot(`filled-form-${role}`);
      });

      // Step 3: Submit the login form and wait for navigation
      await test.step(`Submit login form for ${role}`, async () => {
        test.info().annotations.push({
          type: 'Action',
          description: `Submitting login form for ${role} role`
        });

        // Before submitting, ensure no error message is visible
        const errorElement = page.getByTestId('login-error-message');
        const hasErrorBefore = await errorElement.isVisible().catch(() => false);
        expect(hasErrorBefore, 'Should not have error before submission').toBe(false);

        // Submit form with a timeout to catch failures
        await Promise.race([
          loginPage.submitLoginForm(),
          // Add a timeout that doesn't fail the test but logs an issue
          new Promise(resolve => setTimeout(() => {
            console.warn('Login form submission is taking longer than expected');
            resolve(null);
          }, 10000))
        ]);

        // Take screenshot after form submission
        await loginPage.takeScreenshot(`after-submit-${role}`);

        // Check for error messages after submission
        const hasErrorAfter = await errorElement.isVisible().catch(() => false);
        expect(hasErrorAfter, 'Should not have error after submission').toBe(false);
      });

      // Step 4: Wait for navigation and verify successful login
      await test.step(`Verify successful login for ${role}`, async () => {
        // Get expected URL for the role
        const expectedUrl = loginPage.getExpectedDashboardUrl(role);

        // Wait for navigation to complete with a reasonable timeout
        try {
          await page.waitForURL(`**${expectedUrl}`, { timeout: 15000 });
        } catch (error) {
          // If we time out waiting for the exact URL, take a screenshot and check current URL
          await loginPage.takeScreenshot(`navigation-timeout-${role}`);
          console.warn(`Navigation timeout waiting for ${expectedUrl}, current URL: ${page.url()}`);

          // Verify we're at least not on the login page anymore
          expect(page.url(), 'Should not be on login page after submission').not.toContain('/login');
        }

        // Take screenshot after login
        await loginPage.takeScreenshot(`after-login-${role}`);

        // Extract text from the page for verification
        const pageText = await loginPage.extractPageText();

        // Verify there's no error messages
        expect(pageText, 'Page should not contain login error messages').not.toContain('Invalid email or password');
        expect(pageText, 'Page should not contain login failure messages').not.toContain('Login failed');

        // Verify login was successful using helper method
        const loginSuccessful = await loginPage.verifySuccessfulLogin(role);
        expect(loginSuccessful, `Login should be successful for ${role} role`).toBe(true);

        console.log(`✅ Successfully verified login for ${role} role`);
      });
    });
  }

  /**
   * Test for session persistence
   * Verifies that login session persists after page refresh
   */
  test('Should maintain login session after page refresh', async ({ page }) => {
    const loginPage = new LoginPage(page, DEBUG);

    if (DEBUG) {
      loginPage.enableDebug();
    }

    // Step 1: Login as Admin
    await test.step('Login as Admin', async () => {
      await loginPage.loginAs(Role.Admin, baseURL);

      // Verify login successful
      const isLoggedInBeforeRefresh = await loginPage.verifySuccessfulLogin(Role.Admin);
      expect(isLoggedInBeforeRefresh, 'Should successfully log in as Admin').toBeTruthy();

      // Take screenshot of logged in state
      await loginPage.takeScreenshot('before-refresh');
    });

    // Step 2: Refresh the page and verify session persistence
    await test.step('Refresh page and verify session persistence', async () => {
      // Store the current URL to compare after refresh
      const urlBeforeRefresh = page.url();

      // Refresh the page
      await page.reload();

      // Wait for page to settle after reload
      await page.waitForLoadState('networkidle', { timeout: 10000 });

      // Take screenshot after refresh
      await loginPage.takeScreenshot('after-refresh');

      // Verify still logged in after refresh
      const isLoggedInAfterRefresh = await loginPage.verifySuccessfulLogin(Role.Admin);
      expect(isLoggedInAfterRefresh, 'Should remain logged in after page refresh').toBeTruthy();

      // Verify the URL is the same after refresh
      expect(page.url(), 'URL should remain the same after refresh').toBe(urlBeforeRefresh);

      // Verify specifically not redirected to login page
      expect(page.url(), 'Should not be redirected to login page after refresh').not.toContain('/login');
    });
  });

  /**
   * Test for invalid login credentials
   * Verifies that login fails with invalid credentials and shows appropriate error
   */
  test('Should show error with invalid credentials', async ({ page }) => {
    const loginPage = new LoginPage(page, DEBUG);

    if (DEBUG) {
      loginPage.enableDebug();
    }

    await test.step('Navigate to login page', async () => {
      await loginPage.navigateToLoginPage(baseURL);
    });

    await test.step('Attempt login with invalid credentials', async () => {
      // Use invalid credentials
      await loginPage.fillLoginForm('<EMAIL>', 'wrongpassword');

      // Take screenshot before submit
      await loginPage.takeScreenshot('invalid-credentials-before-submit');

      // Submit form
      await loginPage.submitLoginForm();

      // Take screenshot after submit
      await loginPage.takeScreenshot('invalid-credentials-after-submit');
    });

    await test.step('Verify error message', async () => {
      // Wait for error message to appear
      const errorMessage = page.getByTestId('login-error-message');
      await expect(errorMessage, 'Error message should be visible').toBeVisible({ timeout: 5000 });

      // Verify we're still on the login page
      expect(page.url(), 'Should still be on login page').toContain('/login');
    });
  });
}); 