import { test, expect } from '@playwright/test';
import { Role } from '../../../../src/[types]/Role';
import { LoginPage } from '../pageObjects/login.page';

/**
 * Comprehensive E2E Tests for user login with all roles
 * This test suite verifies that each role can successfully log in
 * and is directed to the appropriate dashboard/homepage.
 */
test.describe('Role-based Login Tests', () => {
  // Define the base URL
  const baseURL = 'http://localhost:3001';

  // Array of ALL roles to test (including Admin)
  const allRoles = [Role.Admin, Role.ClinicAdmin, Role.Doctor, Role.Client, Role.Patient];

  // Test each role with full verification
  for (const role of allRoles) {
    test(`Login test for ${role} role`, async ({ page }) => {
      const loginPage = new LoginPage(page);

      // Step 1: Navigate to login page
      await test.step(`Navigate to login page for ${role}`, async () => {
        await test.info().annotations.push({
          type: 'Test Step',
          description: `Navigating to login page for ${role} role`
        });

        await loginPage.navigateToLoginPage(baseURL);

        // Take screenshot of login form
        await loginPage.takeScreenshot();
        test.info().annotations.push({
          type: 'Screenshot',
          description: `Login form for ${role} role`
        });
      });

      // Step 2: Fill login form with role-specific credentials
      await test.step(`Fill login form for ${role}`, async () => {
        // Get credentials for the current role
        const credentials = loginPage.getCredentialsForRole(role);

        await test.info().annotations.push({
          type: 'Credentials',
          description: `Using email: ${credentials.email} for ${role} role`
        });

        // Fill the login form
        await loginPage.fillLoginForm(credentials.email, credentials.password);

        // Optional: Take screenshot of filled form
        await loginPage.takeScreenshot();
      });

      // Step 3: Submit the login form
      await test.step(`Submit login form for ${role}`, async () => {
        await test.info().annotations.push({
          type: 'Action',
          description: `Submitting login form for ${role} role`
        });

        await loginPage.submitLoginForm();
      });

      // Step 4: Wait for navigation and verify successful login
      await test.step(`Verify successful login for ${role}`, async () => {
        // Get expected URL for the role *before* waiting
        const expectedUrl = loginPage.getExpectedDashboardUrl(role);
        // Wait specifically for the expected URL pattern to load
        await page.waitForURL(`**${expectedUrl}`);

        // Take screenshot after login
        await loginPage.takeScreenshot();
        test.info().annotations.push({
          type: 'Screenshot',
          description: `${role} dashboard after login`
        });

        // Extract text from the page for verification
        const pageText = await loginPage.extractPageText();

        // Robust URL comparison: compare only the path, ignore host/protocol
        const currentUrlObj = new URL(page.url());
        const expectedUrlObj = new URL(expectedUrl, baseURL);
        expect(currentUrlObj.pathname, `Pathname should match expected dashboard path for ${role}`).toBe(expectedUrlObj.pathname);

        // Verify there's no error messages
        expect(pageText, 'Page should not contain login error messages').not.toContain('Invalid email or password');
        expect(pageText, 'Page should not contain login failure messages').not.toContain('Login failed');

        // Role-specific dashboard content verification removed - will be handled in routing/dashboard tests.

        // Verify login was successful using helper method
        const loginSuccessful = await loginPage.verifySuccessfulLogin(role);
        expect(loginSuccessful, `Login should be successful for ${role} role`).toBe(true);

        console.log(`✅ Successfully verified login for ${role} role`);
      });

      // Step 5 (Verify role-specific navigation access) removed - will be handled in routing tests.
    });
  }

  // Test for unauthorized access attempt removed - will be handled in routing tests.

  /**
   * Test for session persistence
   * Verifies that login session persists after page refresh
   */
  test('Should maintain login session after page refresh', async ({ page }) => {
    const loginPage = new LoginPage(page);

    // Login as Admin
    await loginPage.loginAs(Role.Admin, baseURL);

    // Verify login successful
    const isLoggedInBeforeRefresh = await loginPage.verifySuccessfulLogin(Role.Admin);
    expect(isLoggedInBeforeRefresh, 'Should successfully log in as Admin').toBeTruthy();

    // Refresh the page
    await page.reload();

    // Wait for page to reload
    await page.waitForTimeout(3000);

    // Take screenshot after refresh
    await loginPage.takeScreenshot();

    // Verify still logged in after refresh
    const isLoggedInAfterRefresh = await loginPage.verifySuccessfulLogin(Role.Admin);
    expect(isLoggedInAfterRefresh, 'Should remain logged in after page refresh').toBeTruthy();

    // Verify not redirected to login page
    const currentUrl = page.url();
    expect(currentUrl, 'Should not be redirected to login page after refresh').not.toContain('/login');
  });
});
