import { Page } from '@playwright/test';
import { Role } from 'RBAC/[types]/Role';
import ROUTES from 'Routing/appRoutes';

/**
 * Page Object for the Login page
 * Implements methods for interacting with the login page using Stagehand
 */
export class LoginPage {
  readonly page: Page;
  private debug: boolean;

  constructor(page: Page, debug = false) {
    this.page = page;
    this.debug = debug;
    this.log('LoginPage initialized');
  }

  /**
   * Internal logging method that only logs when debug is enabled
   * @param message Message to log
   * @param args Optional arguments to log
   */
  private log(message: string, ...args: any[]): void {
    if (this.debug) {
      console.log(`[LoginPage] ${message}`, ...args);
    }
  }

  /**
   * Enable debugging
   */
  enableDebug(): void {
    this.debug = true;
    this.log('Debug mode enabled');
  }

  /**
   * Navigate to the login page
   * @param baseURL Base URL of the application
   */
  async navigateToLoginPage(baseURL: string): Promise<void> {
    const loginUrl = `${baseURL}/trq/login`;
    this.log(`Navigating to ${loginUrl}`);

    // Use <PERSON>wright's native goto method
    await this.page.goto(loginUrl);
    this.log('Navigation complete');

    // Verify we're on the login page
    try {
      await this.page.waitForSelector('[data-testid="login-email-input"]', { timeout: 5000 });
      this.log('Login form found');
    } catch (error: any) {
      console.error('Login form not found on page:', error);
      // Take a screenshot for debugging
      await this.page.screenshot({ path: 'test-results/login-page-error.png' });
      throw new Error(`Login form not found after navigation: ${error.message}`);
    }
  }

  /**
   * Fill in the login form
   * @param email User's email
   * @param password User's password
   */
  async fillLoginForm(email: string, password: string): Promise<void> {
    this.log(`Filling login form with email: ${email}`);

    try {
      // Fill email field using data-testid
      const emailInput = this.page.getByTestId('login-email-input');
      await emailInput.fill(email);
      this.log('Email field filled');

      // Fill password field using data-testid
      const passwordInput = this.page.getByTestId('login-password-input');
      await passwordInput.fill(password);
      this.log('Password field filled');
    } catch (error: any) {
      console.error('Error filling login form:', error);
      await this.page.screenshot({ path: 'test-results/login-form-fill-error.png' });
      throw new Error(`Failed to fill login form: ${error.message}`);
    }
  }

  /**
   * Submit the login form
   */
  async submitLoginForm(): Promise<void> {
    this.log('Submitting login form');

    try {
      // Click the submit button using data-testid
      const submitButton = this.page.getByTestId('login-submit-button');

      // Ensure the button is visible and enabled
      await submitButton.waitFor({ state: 'visible', timeout: 5000 });

      // Take screenshot before clicking
      if (this.debug) {
        await this.page.screenshot({ path: 'test-results/before-login-submit.png' });
      }

      await submitButton.click();
      this.log('Login form submitted');

      // Wait for network idle to ensure the form submission completes
      await this.page.waitForLoadState('networkidle', { timeout: 10000 });
      this.log('Network activity settled after login');
    } catch (error: any) {
      console.error('Error submitting login form:', error);
      await this.page.screenshot({ path: 'test-results/login-submit-error.png' });
      throw new Error(`Failed to submit login form: ${error.message}`);
    }
  }

  /**
   * Take a screenshot of the current page
   * @param name Optional name for the screenshot
   * @returns Promise that resolves with the screenshot data (Buffer)
   */
  async takeScreenshot(name?: string): Promise<Buffer> {
    const filename = name ? `test-results/${name}.png` : undefined;
    this.log(`Taking screenshot${filename ? ` to ${filename}` : ''}`);

    // Use Playwright's native screenshot method
    if (filename) {
      return await this.page.screenshot({ path: filename });
    } else {
      return await this.page.screenshot();
    }
  }

  /**
   * Extract all text from the current page
   * @returns Promise that resolves with the page's body text content, or null if not found.
   */
  async extractPageText(): Promise<string | null> {
    this.log('Extracting page text');

    // Use Playwright's native textContent method on the body
    const text = await this.page.locator('body').textContent();
    this.log(`Extracted ${text?.length || 0} characters of text`);
    return text;
  }

  /**
   * Complete the login process for a specific role
   * @param role The role to login as
   * @param baseURL Base URL of the application
   */
  async loginAs(role: Role, baseURL: string): Promise<void> {
    this.log(`Logging in as role: ${role}`);

    // Get credentials for the role
    const credentials = this.getCredentialsForRole(role);

    // Navigate to login page
    await this.navigateToLoginPage(baseURL);

    // Fill and submit login form
    await this.fillLoginForm(credentials.email, credentials.password);

    // Take screenshot before submitting
    if (this.debug) {
      await this.takeScreenshot(`login-as-${role}-before-submit`);
    }

    await this.submitLoginForm();

    // Wait for navigation to complete - Using waitForURL in the test spec is preferred
    this.log('Waiting for navigation after login');

    try {
      // Wait for URL change indicating successful login
      await this.page.waitForURL(`**${baseURL}/trq/**`, { timeout: 10000 });
      this.log(`Navigation complete, current URL: ${this.page.url()}`);
    } catch (error: any) {
      console.error('Navigation timeout after login:', error);
      await this.takeScreenshot(`login-as-${role}-navigation-error`);

      // Don't throw, let the verification method handle the result
      this.log(`Current URL after timeout: ${this.page.url()}`);
    }

    // Take screenshot after login attempt
    await this.takeScreenshot(`login-as-${role}-after-login`);
  }

  /**
   * Get credentials for a specific role
   * @param role The role to get credentials for
   * @returns Object with email and password for the role
   */
  getCredentialsForRole(role: Role): { email: string; password: string } {
    this.log(`Getting credentials for role: ${role}`);

    switch (role) {
      case Role.Admin:
        return {
          email: process.env.E2E_ADMIN_EMAIL || '<EMAIL>',
          password: process.env.E2E_ADMIN_PASSWORD || 'password123'
        };
      case Role.ClinicAdmin:
        return {
          email: process.env.E2E_CLINIC_ADMIN_EMAIL || '<EMAIL>',
          password: process.env.E2E_CLINIC_ADMIN_PASSWORD || 'password123'
        };
      case Role.Doctor:
        return {
          email: process.env.E2E_DOCTOR_EMAIL || '<EMAIL>',
          password: process.env.E2E_DOCTOR_PASSWORD || 'password123'
        };
      case Role.Client:
        return {
          email: process.env.E2E_CLIENT_EMAIL || '<EMAIL>',
          password: process.env.E2E_CLIENT_PASSWORD || 'password123'
        };
      case Role.Patient:
        return {
          email: process.env.E2E_PATIENT_EMAIL || '<EMAIL>',
          password: process.env.E2E_PATIENT_PASSWORD || 'password123'
        };
      default:
        this.log(`Unsupported role: ${role}, using default Admin credentials`);
        return {
          email: process.env.E2E_ADMIN_EMAIL || '<EMAIL>',
          password: process.env.E2E_ADMIN_PASSWORD || 'password123'
        };
    }
  }

  /**
   * Verify the login was successful for a specific role
   * @param role The role that was logged in
   * @returns Promise that resolves to true if login was successful
   */
  async verifySuccessfulLogin(role: Role): Promise<boolean> {
    this.log(`Verifying successful login for role: ${role}`);

    try {
      // Take a screenshot for debugging
      await this.takeScreenshot(`verify-login-${role}`);

      // Check there are no error messages (using testid for login error)
      const loginError = await this.page
        .getByTestId('login-error-message')
        .isVisible()
        .catch(() => false);

      if (loginError) {
        console.error('Login failed with error message');
        return false;
      }

      // Get expected URL for the role
      const expectedUrl = this.getExpectedDashboardUrl(role);
      const currentUrl = this.page.url();
      console.log(`Expected URL for role ${role}: ${expectedUrl}`);
      console.log(`Current URL for role ${role}: ${currentUrl}`);

      // Check if we're still on the login page - if we are, login failed
      const isOnLoginPage = currentUrl.includes('/login');
      if (isOnLoginPage) {
        console.error('Still on login page after authentication attempt');
        return false;
      }

      // Check if we're on the expected dashboard URL path
      const currentUrlPath = new URL(currentUrl).pathname;
      const expectedUrlPath = expectedUrl;

      // A successful login means we're not on the login page
      // and ideally on the expected dashboard path
      if (!isOnLoginPage && currentUrlPath.includes(expectedUrlPath)) {
        // We're on the expected dashboard path - ideal case
        this.log('Login successful - on expected dashboard path');
        return true;
      }

      // If we're not on the login page but not on the expected dashboard path,
      // we'll still consider it successful but log a warning
      if (!isOnLoginPage) {
        console.warn(`Login successful but redirected to ${currentUrlPath} instead of ${expectedUrlPath}`);
        return true;
      }

      return false;
    } catch (error: any) {
      console.error('Error in verifySuccessfulLogin:', error);
      await this.takeScreenshot('verification-error');
      return false;
    }
  }

  /**
   * Get the expected dashboard URL for a specific role
   * @param role The role to get the URL for
   * @returns The expected dashboard URL
   */
  getExpectedDashboardUrl(role: Role): string {
    this.log(`Getting expected dashboard URL for role: ${role}`);

    // Return the appropriate dashboard URL based on the role
    switch (role) {
      case Role.Admin:
        return ROUTES.HOMEPAGES.ADMIN;
      case Role.ClinicAdmin:
        return ROUTES.HOMEPAGES.CLINIC_ADMIN;
      case Role.Doctor:
        return ROUTES.HOMEPAGES.DOCTOR;
      case Role.Client:
        return ROUTES.HOMEPAGES.CLIENT;
      case Role.Patient:
        return ROUTES.HOMEPAGES.PATIENT;
      default:
        throw new Error(`Unsupported role: ${role}`);
    }
  }

  /**
   * Check if the user is logged in
   * @returns Promise that resolves to true if the user is logged in
   */
  async isLoggedIn(): Promise<boolean> {
    this.log('Checking if user is logged in');

    try {
      // Check if login form is visible
      const loginFormVisible = await this.page
        .getByTestId('login-email-input')
        .isVisible()
        .catch(() => false);

      if (loginFormVisible) {
        this.log('Login form is visible, user is not logged in');
        return false;
      }

      // Check if we're on a protected route
      const currentUrl = this.page.url();
      const isOnProtectedRoute = currentUrl.includes('/trq/') && !currentUrl.includes('/login');

      this.log(`Current URL: ${currentUrl}, on protected route: ${isOnProtectedRoute}`);
      return isOnProtectedRoute;
    } catch (error: any) {
      console.error('Error checking login status:', error);
      return false;
    }
  }
}
