import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  serverTimestamp,
  writeBatch,
  QueryConstraint
} from 'firebase/firestore';
import { db } from '../../Firebase/[config]/firebase';
import {
  Notification,
  NotificationStatus,
  NotificationPriority,
  NotificationChannel,
  NotificationType,
  CreateNotificationPayload,
  UserNotificationPreferences
} from '../[types]/Notification';

const NOTIFICATIONS_COLLECTION = 'notifications';
const NOTIFICATION_PREFERENCES_COLLECTION = 'notificationPreferences';

/**
 * Service for managing notifications
 */
export class NotificationService {
  /**
   * Create a new notification
   * @param payload Notification creation payload
   * @returns Promise with the created notification ID
   */
  async createNotification(payload: CreateNotificationPayload): Promise<string> {
    try {
      const notificationData: Omit<Notification, 'id'> = {
        userId: payload.userId,
        type: payload.type,
        title: payload.title,
        message: payload.message,
        status: NotificationStatus.UNREAD,
        priority: payload.priority || NotificationPriority.MEDIUM,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        channels: payload.channels || [NotificationChannel.IN_APP],
        ...(payload.actions && { actions: payload.actions }),
        ...(payload.imageUrl && { imageUrl: payload.imageUrl }),
        ...(payload.linkUrl && { linkUrl: payload.linkUrl }),
        ...(payload.sourceId && { sourceId: payload.sourceId }),
        ...(payload.sourceType && { sourceType: payload.sourceType }),
        ...(payload.metadata && { metadata: payload.metadata }),
        ...(payload.expiresAt && { expiresAt: Timestamp.fromDate(payload.expiresAt) })
      };

      const docRef = await addDoc(collection(db, NOTIFICATIONS_COLLECTION), notificationData);
      return docRef.id;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  /**
   * Get a notification by ID
   * @param notificationId Notification ID
   * @returns Promise with the notification or null if not found
   */
  async getNotification(notificationId: string): Promise<Notification | null> {
    try {
      const docRef = doc(db, NOTIFICATIONS_COLLECTION, notificationId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Notification;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error getting notification:', error);
      throw error;
    }
  }

  /**
   * Get notifications for a user
   * @param userId User ID
   * @param status Optional status filter
   * @param limit Optional limit of notifications to return
   * @returns Promise with an array of notifications
   */
  async getUserNotifications(userId: string, status?: NotificationStatus, maxResults: number = 50): Promise<Notification[]> {
    try {
      const constraints: QueryConstraint[] = [where('userId', '==', userId), orderBy('createdAt', 'desc'), limit(maxResults)];

      if (status) {
        constraints.push(where('status', '==', status));
      }

      const q = query(collection(db, NOTIFICATIONS_COLLECTION), ...constraints);
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data()
      })) as Notification[];
    } catch (error) {
      console.error('Error getting user notifications:', error);
      throw error;
    }
  }

  /**
   * Mark a notification as read
   * @param notificationId Notification ID
   * @returns Promise that resolves when the operation is complete
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      const docRef = doc(db, NOTIFICATIONS_COLLECTION, notificationId);
      await updateDoc(docRef, {
        status: NotificationStatus.READ,
        readAt: Timestamp.now(),
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }

  /**
   * Mark all notifications for a user as read
   * @param userId User ID
   * @returns Promise that resolves when the operation is complete
   */
  async markAllAsRead(userId: string): Promise<void> {
    try {
      const q = query(
        collection(db, NOTIFICATIONS_COLLECTION),
        where('userId', '==', userId),
        where('status', '==', NotificationStatus.UNREAD)
      );

      const querySnapshot = await getDocs(q);

      const batch = writeBatch(db);
      const now = Timestamp.now();

      querySnapshot.docs.forEach((document) => {
        const docRef = document.ref;
        batch.update(docRef, {
          status: NotificationStatus.READ,
          readAt: now,
          updatedAt: serverTimestamp()
        });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  /**
   * Archive a notification
   * @param notificationId Notification ID
   * @returns Promise that resolves when the operation is complete
   */
  async archiveNotification(notificationId: string): Promise<void> {
    try {
      const docRef = doc(db, NOTIFICATIONS_COLLECTION, notificationId);
      await updateDoc(docRef, {
        status: NotificationStatus.ARCHIVED,
        updatedAt: serverTimestamp()
      });
    } catch (error) {
      console.error('Error archiving notification:', error);
      throw error;
    }
  }

  /**
   * Delete a notification
   * @param notificationId Notification ID
   * @returns Promise that resolves when the operation is complete
   */
  async deleteNotification(notificationId: string): Promise<void> {
    try {
      const docRef = doc(db, NOTIFICATIONS_COLLECTION, notificationId);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }

  /**
   * Get unread notification count for a user
   * @param userId User ID
   * @returns Promise with the count of unread notifications
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      const q = query(
        collection(db, NOTIFICATIONS_COLLECTION),
        where('userId', '==', userId),
        where('status', '==', NotificationStatus.UNREAD)
      );

      const querySnapshot = await getDocs(q);
      return querySnapshot.size;
    } catch (error) {
      console.error('Error getting unread notification count:', error);
      throw error;
    }
  }

  /**
   * Get user notification preferences
   * @param userId User ID
   * @returns Promise with the user's notification preferences or null if not found
   */
  async getUserPreferences(userId: string): Promise<UserNotificationPreferences | null> {
    try {
      const docRef = doc(db, NOTIFICATION_PREFERENCES_COLLECTION, userId);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        return docSnap.data() as UserNotificationPreferences;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error getting user notification preferences:', error);
      throw error;
    }
  }

  /**
   * Update user notification preferences
   * @param preferences User notification preferences
   * @returns Promise that resolves when the operation is complete
   */
  async updateUserPreferences(preferences: UserNotificationPreferences): Promise<void> {
    try {
      const docRef = doc(db, NOTIFICATION_PREFERENCES_COLLECTION, preferences.userId);
      const updatedPreferences = {
        ...preferences,
        updatedAt: serverTimestamp()
      };

      // Convert to a plain object to avoid Firestore serialization issues
      const preferencesObject = JSON.parse(JSON.stringify(updatedPreferences));
      await updateDoc(docRef, preferencesObject);
    } catch (error) {
      console.error('Error updating user notification preferences:', error);
      throw error;
    }
  }

  /**
   * Create default notification preferences for a user
   * @param userId User ID
   * @returns Promise that resolves when the operation is complete
   */
  async createDefaultPreferences(userId: string): Promise<void> {
    try {
      // Create default preferences for all notification types
      const typesPreferences = {} as {
        [key in NotificationType]: { enabled: boolean; channels: NotificationChannel[] };
      };

      // Set default preferences for each notification type
      Object.values(NotificationType).forEach((type) => {
        typesPreferences[type as NotificationType] = {
          enabled: true,
          channels: [NotificationChannel.IN_APP, ...(type !== NotificationType.SYSTEM ? [NotificationChannel.EMAIL] : [])]
        };
      });

      const defaultPreferences: UserNotificationPreferences = {
        userId,
        channels: {
          [NotificationChannel.IN_APP]: true,
          [NotificationChannel.EMAIL]: true,
          [NotificationChannel.PUSH]: false,
          [NotificationChannel.SMS]: false
        },
        types: typesPreferences,
        doNotDisturb: {
          enabled: false
        },
        updatedAt: Timestamp.now()
      };

      const docRef = doc(db, NOTIFICATION_PREFERENCES_COLLECTION, userId);
      // Use setDoc instead of updateDoc for creating new documents
      const { setDoc } = await import('firebase/firestore');
      await setDoc(docRef, defaultPreferences);
    } catch (error) {
      console.error('Error creating default notification preferences:', error);
      throw error;
    }
  }
}

// Export a singleton instance of the service
export const notificationService = new NotificationService();
