import { Role } from '../[types]/Role';
import ROUTES from '../../Routing/appRoutes';

// Placeholder IDs used in appRoutes.ts for generating navigable paths
const testId = 'test-id';
const testPurchaseId = 'test-purchase-id';
// const testUserId = 'test-user-id'; // Available if needed for specific user routes

interface RoutePermissionConfig {
  path: string;
  allowedRoles: Role[];
}

// Define permissions for routes. Add other roles as needed.
// For now, focusing on Patient role based on E2E test findings.
const routePermissionsList: RoutePermissionConfig[] = [
  // --- HOMEPAGES ---
  { path: ROUTES.HOMEPAGES.ADMIN, allowedRoles: [Role.Admin] },
  { path: ROUTES.HOMEPAGES.CLINIC_ADMIN, allowedRoles: [Role.ClinicAdmin, Role.Admin] },
  { path: ROUTES.HOMEPAGES.DOCTOR, allowedRoles: [Role.Doctor, Role.Admin] },
  { path: ROUTES.HOMEPAGES.CLIENT, allowedRoles: [Role.Client, Role.Admin] }, // Assuming Client has a homepage
  { path: ROUTES.HOMEPAGES.PATIENT, allowedRoles: [Role.Patient, Role.Admin, Role.Doctor, Role.ClinicAdmin, Role.Client] }, // All roles can see their respective homepages or be redirected

  // --- AUTH (publicly accessible or handled by specific guards/layouts) ---
  // These are typically not checked by TRQAuthGuard's canAccessRoute if they are outside its scope
  // For completeness, if any /trq/auth/* routes were accidentally under TRQAuthGuard:
  { path: ROUTES.AUTH.LOGIN, allowedRoles: [Role.Admin, Role.Client, Role.ClinicAdmin, Role.Doctor, Role.Patient] }, // Public, but if guarded, all can retry
  { path: ROUTES.AUTH.REGISTER, allowedRoles: [Role.Admin, Role.Client, Role.ClinicAdmin, Role.Doctor, Role.Patient] }, // Public
  { path: ROUTES.AUTH.UNAUTHORIZED, allowedRoles: [Role.Admin, Role.Client, Role.ClinicAdmin, Role.Doctor, Role.Patient] }, // All authenticated users should be able to see this

  // --- ADMIN --- (Generally Admin only)
  { path: ROUTES.ADMIN.ROLES, allowedRoles: [Role.Admin] },
  { path: ROUTES.ADMIN.PERMISSIONS, allowedRoles: [Role.Admin] },
  { path: ROUTES.ADMIN.DASHBOARD, allowedRoles: [Role.Admin] }, // Same as HOMEPAGES.ADMIN
  { path: ROUTES.ADMIN.ONBOARDING, allowedRoles: [Role.Admin] },

  // --- USERS --- (Generally Admin, ClinicAdmin)
  { path: ROUTES.USERS.LIST, allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.USERS.DETAILS(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.USERS.EDIT(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.USERS.ADD, allowedRoles: [Role.Admin, Role.ClinicAdmin] },

  // --- CLIENTS --- (Admin, ClinicAdmin, Doctor for some views)
  { path: ROUTES.CLIENTS.LIST, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.CLIENTS.DETAILS(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.CLIENTS.EDIT(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.CLIENTS.PROFILE(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.CLIENTS.ADD, allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.CLIENTS.ADD_PATIENT, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.CLIENTS.BILLING, allowedRoles: [Role.Admin, Role.ClinicAdmin] },

  // --- DOCTORS --- (Admin, ClinicAdmin for management, Doctor for own info)
  { path: ROUTES.DOCTORS.LIST, allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.DOCTORS.DETAILS(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.DOCTORS.EDIT(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.DOCTORS.PROFILE(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.DOCTORS.ADD, allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.DOCTORS.MY_PATIENTS, allowedRoles: [Role.Doctor, Role.Admin, Role.ClinicAdmin] },

  // --- CLINIC ADMINS --- (Admin for management, ClinicAdmin for own info)
  { path: ROUTES.CLINIC_ADMINS.LIST, allowedRoles: [Role.Admin, Role.ClinicAdmin] }, // List view
  { path: ROUTES.CLINIC_ADMINS.DETAILS(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.CLINIC_ADMINS.EDIT(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.CLINIC_ADMINS.PROFILE, allowedRoles: [Role.Admin, Role.ClinicAdmin] }, // Static profile route
  { path: ROUTES.CLINIC_ADMINS.ADD, allowedRoles: [Role.Admin] },

  // --- CLINICS --- (Admin, ClinicAdmin)
  { path: ROUTES.CLINICS.LIST, allowedRoles: [Role.Admin, Role.ClinicAdmin] }, // List view
  { path: ROUTES.CLINICS.MY_CLINIC, allowedRoles: [Role.ClinicAdmin, Role.Admin] },
  { path: ROUTES.CLINICS.DETAILS(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.CLINICS.EDIT(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.CLINICS.ADD, allowedRoles: [Role.Admin] },

  // --- PATIENTS --- (Patient for own, Doctor/ClinicAdmin/Admin for relevant views)
  { path: ROUTES.PATIENTS.HOME, allowedRoles: [Role.Patient, Role.Admin, Role.Doctor, Role.ClinicAdmin] }, // Same as HOMEPAGES.PATIENT
  { path: ROUTES.PATIENTS.DETAILS(testId), allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin] },
  { path: ROUTES.PATIENTS.EDIT(testId), allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin] },
  { path: ROUTES.PATIENTS.PROFILE(testId), allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin] },
  // Note: PATIENTS.LIST, PATIENTS.ADD are typically Admin/ClinicAdmin/Doctor functions, not for Patients themselves.
  { path: ROUTES.PATIENTS.LIST, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.PATIENTS.ADD, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },

  // --- QUESTIONNAIRES ---
  { path: ROUTES.QUESTIONNAIRES.LIST, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] }, // General listing/management
  { path: ROUTES.QUESTIONNAIRES.MY_QUESTIONNAIRES, allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin] }, // Patient's own list
  { path: ROUTES.QUESTIONNAIRES.DETAILS(testId), allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin] },
  { path: ROUTES.QUESTIONNAIRES.WIZARD(testId), allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin] },
  { path: ROUTES.QUESTIONNAIRES.ADD, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.QUESTIONNAIRES.EDIT(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },

  // --- COMPLIANCE REPORTS ---
  { path: ROUTES.COMPLIANCE_REPORTS.LIST, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.COMPLIANCE_REPORTS.DETAILS(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.COMPLIANCE_REPORTS.EDIT(testId), allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },
  { path: ROUTES.COMPLIANCE_REPORTS.REVIEW(testId), allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin] }, // Patient can review their own

  // --- OTHERS (General App Features) ---
  { path: ROUTES.OTHERS.SETTINGS, allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin, Role.Client] },
  { path: ROUTES.OTHERS.HEALTH_INSIGHTS, allowedRoles: [Role.Patient, Role.Doctor, Role.Admin] },
  { path: ROUTES.OTHERS.APPOINTMENTS, allowedRoles: [Role.Patient, Role.Doctor, Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.OTHERS.MESSAGES, allowedRoles: [Role.Patient, Role.Doctor, Role.Admin, Role.ClinicAdmin, Role.Client] },
  { path: ROUTES.OTHERS.MEDICAL_RECORDS, allowedRoles: [Role.Patient, Role.Doctor, Role.Admin, Role.ClinicAdmin] },
  { path: ROUTES.OTHERS.HOME, allowedRoles: [Role.Patient, Role.Doctor, Role.ClinicAdmin, Role.Admin, Role.Client] }, // Generic home, usually redirects
  { path: ROUTES.OTHERS.REPORTS, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] }, // General reports section

  // --- ANALYTICS ---
  { path: ROUTES.ANALYTICS.BASE, allowedRoles: [Role.Admin, Role.ClinicAdmin, Role.Doctor] },

  // --- PRODUCTS (Assuming an e-commerce like feature) ---
  { path: ROUTES.PRODUCTS.LIST, allowedRoles: [Role.Admin] }, // Product management
  { path: ROUTES.PRODUCTS.ADD, allowedRoles: [Role.Admin] },
  { path: ROUTES.PRODUCTS.EDIT(testId), allowedRoles: [Role.Admin] },
  { path: ROUTES.PRODUCTS.DETAILS(testId), allowedRoles: [Role.Admin, Role.Patient, Role.Client] }, // All can view product details

  // --- AI CHAT ---
  { path: ROUTES.AI_CHAT.BASE, allowedRoles: [Role.Patient, Role.Doctor, Role.Admin, Role.ClinicAdmin, Role.Client] }, // Broad access

  // --- PURCHASES ---
  { path: ROUTES.PURCHASES.LIST, allowedRoles: [Role.Admin] }, // Admin view of all purchases
  { path: ROUTES.PURCHASES.CART, allowedRoles: [Role.Patient, Role.Client, Role.Admin] }, // Assuming clients can also purchase
  { path: ROUTES.PURCHASES.CHECKOUT, allowedRoles: [Role.Patient, Role.Client, Role.Admin] },
  { path: ROUTES.PURCHASES.MY_PURCHASES, allowedRoles: [Role.Patient, Role.Client, Role.Admin] }, // User's own purchases
  { path: ROUTES.PURCHASES.DETAILS(testPurchaseId), allowedRoles: [Role.Patient, Role.Client, Role.Admin] },
  { path: ROUTES.PURCHASES.EDIT(testPurchaseId), allowedRoles: [Role.Admin] }, // Only Admin can edit purchase records?
  { path: ROUTES.PURCHASES.ADD, allowedRoles: [Role.Admin] } // Admin manually adding a purchase?
];

/**
 * Checks if a given role can access a specific path.
 * @param role The user's current role.
 * @param path The path they are trying to access.
 * @returns True if access is allowed, false otherwise.
 */
export function canAccessRoute(role: Role | null | undefined, path: string): boolean {
  if (!role) {
    return false; // No role, typically means not authenticated or role not set
  }

  const normalizedPath = path.endsWith('/') && path.length > 1 ? path.slice(0, -1) : path;

  const permission = routePermissionsList.find((p) => p.path === normalizedPath);

  if (permission) {
    return permission.allowedRoles.includes(role);
  }

  // Default behavior for unlisted routes: For now, deny access.
  // This makes unconfigured routes secure by default.
  // Consider if some routes should be accessible by any authenticated user by default.
  console.warn(`No explicit permission defined for path: ${normalizedPath}. Denying access by default.`);
  return false;
}
